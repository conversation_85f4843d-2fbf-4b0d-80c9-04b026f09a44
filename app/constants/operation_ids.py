from dataclasses import dataclass


__all__ = ['operation_ids']


@dataclass(frozen=True)
class AuthOperations:
    CREATE_SIGNAL_R_JWT: str = 'create_signal_r_jwt'


@dataclass(frozen=True)
class ConversationOperations:
    CREATE: str = 'create_conversation'
    GET: str = 'get_conversation'
    LIST: str = 'list_conversations'
    UPDATE: str = 'update_conversation'
    DELETE: str = 'delete_conversation'
    GET_EXTRA_DATA: str = 'get_conversation_extra_data'  # TODO: remove after QA don't need it
    UPDATE_QUAL_ID: str = 'update_conversation_qual_id'
    ENGAGEMENT_CHAT_CREATE: str = 'engagement_chat_create'


@dataclass(frozen=True)
class MessageOperations:
    CREATE: str = 'create_message'
    GET: str = 'get_message'
    LIST: str = 'list_messages'
    GET_LAST: str = 'get_last_message'
    UPDATE: str = 'update_message'
    DELETE: str = 'delete_message'


@dataclass(frozen=True)
class KXDashOperations:
    LIST: str = 'list_activities'
    GET: str = 'get_activity'


@dataclass(frozen=True)
class RootOperations:
    HEALTH_CHECK: str = 'health_check'


@dataclass(frozen=True)
class DocumentOperationIds:
    CREATE = 'create_document'


@dataclass(frozen=True)
class ExtractedDataSummaryOperations:
    GET: str = 'get_extracted_data_summary'


@dataclass(frozen=True)
class OperationIds:
    auth: AuthOperations = AuthOperations()
    conversation: ConversationOperations = ConversationOperations()
    message: MessageOperations = MessageOperations()
    kx_dash: KXDashOperations = KXDashOperations()
    root: RootOperations = RootOperations()
    document: DocumentOperationIds = DocumentOperationIds()
    extracted_data_summary: ExtractedDataSummaryOperations = ExtractedDataSummaryOperations()


operation_ids = OperationIds()
