from .base import ApplicationError


__all__ = ['DataCollectionCompleteError']


class DataCollectionCompleteError(ApplicationError):
    """Raised when attempting to perform operations on conversations where data collection is already complete."""

    def __init__(self, operation: str):
        self.operation = operation
        message = (
            f"Cannot perform '{operation}' operation because data collection for this conversation is already complete."
        )
        super().__init__(message)
