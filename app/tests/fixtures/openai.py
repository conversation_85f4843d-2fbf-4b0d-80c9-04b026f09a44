from typing import Generator
from unittest.mock import AsyncMock, patch

import pytest

from constants.message import ConversationMessageIntention
from schemas.conversation_message import ConversationMessageIntentClassifierServiceResponse
from schemas.translation import TranslationLLMResponse


@pytest.fixture(autouse=True)
def openai_mock() -> Generator[AsyncMock, None, None]:
    """Fixture that provides a mock for the OpenAI repository's generate_chat_completion method."""
    with patch('repositories.OpenAIRepository.generate_chat_completion') as mock:

        async def mock_generate(*args, **kwargs):
            response_format = kwargs.get('response_format')
            if response_format == ConversationMessageIntentClassifierServiceResponse:
                return ConversationMessageIntentClassifierServiceResponse(
                    intention=ConversationMessageIntention.UNDEFINED
                )
            elif response_format == TranslationLLMResponse:
                return TranslationLLMResponse(text='Translated content')
            return AsyncMock()

        mock.side_effect = mock_generate
        yield mock


@pytest.fixture(autouse=True)
def openai_text_completion_mock() -> Generator[AsyncMock, None, None]:
    """Fixture that provides a mock for the OpenAI repository's generate_text_completion method."""
    with patch('repositories.OpenAIRepository.generate_text_completion') as mock:
        mock.return_value = 'Mocked text completion content'
        yield mock
