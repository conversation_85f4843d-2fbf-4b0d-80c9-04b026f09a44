"""Tests for data collection validation logic."""

from unittest.mock import AsyncMock, patch
from uuid import UUID

from fastapi import status
import pytest

from constants.extracted_data import ConversationState
from constants.message import PageType
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from core.urls import URL<PERSON><PERSON><PERSON>ver
from schemas import ConfirmedData, ConversationQualIdUpdateRequest, StartAssistantModeRequest
from schemas.ldmf_countries import CountryData


class TestDataCollectionValidation:
    """Test validation logic for operations when data collection is complete."""

    @pytest.mark.parametrize(
        'conversation_state',
        [
            ConversationState.DATA_COMPLETE,
            ConversationState.READY_FOR_QUAL_CREATION,
            ConversationState.QUAL_CREATED,
        ],
    )
    async def test_update_qual_id_raises_error_when_data_collection_complete(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        conversation_data: dict,
        conversation_repository_dep_with_autocommit,
        conversation_state: ConversationState,
    ):
        """Test that update_qual_id raises DataCollectionCompleteError when data collection is complete."""
        # Create a conversation
        create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
        conversation_id = UUID(create_response.json()['conversation']['id'])

        # Set conversation state to data collection complete
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2024-01-01', '2024-12-31'),
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        await conversation_repository_dep_with_autocommit.update_confirmed_data_and_state(
            conversation_id, confirmed_data, conversation_state
        )

        # Attempt to update qual_id
        update_url = url_resolver.reverse(operation_ids.conversation.UPDATE_QUAL_ID, conversation_id=conversation_id)
        update_data = ConversationQualIdUpdateRequest(qual_id='TEST-QUAL-123')

        response = await async_client.patch(update_url, headers=auth_header, json=update_data.model_dump())

        # Should succeed
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data['qual_id'] == 'TEST-QUAL-123'

    @pytest.mark.parametrize(
        'conversation_state',
        [
            ConversationState.INITIAL,
            ConversationState.COLLECTING_CLIENT_NAME,
            ConversationState.COLLECTING_COUNTRY,
            ConversationState.COLLECTING_DATES,
            ConversationState.COLLECTING_OBJECTIVE,
            ConversationState.COLLECTING_OUTCOMES,
            ConversationState.COLLECTING_ADDITIONAL_DATA,
        ],
    )
    async def test_update_qual_id_raises_error_when_data_collection_not_complete(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        conversation_data: dict,
        conversation_repository_dep_with_autocommit,
        conversation_state: ConversationState,
    ):
        """Test that update_qual_id raises DataCollectionCompleteError when data collection is not complete."""
        # Create a conversation
        create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
        conversation_id = UUID(create_response.json()['conversation']['id'])

        # Set conversation state to not complete
        confirmed_data = ConfirmedData()
        await conversation_repository_dep_with_autocommit.update_confirmed_data_and_state(
            conversation_id, confirmed_data, conversation_state
        )

        # Attempt to update qual_id
        update_url = url_resolver.reverse(operation_ids.conversation.UPDATE_QUAL_ID, conversation_id=conversation_id)
        update_data = ConversationQualIdUpdateRequest(qual_id='TEST-QUAL-123')

        response = await async_client.patch(update_url, headers=auth_header, json=update_data.model_dump())

        # Should return 400 Bad Request
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        error_data = response.json()
        assert 'update_qual_id' in error_data['detail']
        assert 'data collection' in error_data['detail'].lower()

    @pytest.mark.parametrize(
        'conversation_state',
        [
            ConversationState.DATA_COMPLETE,
            ConversationState.READY_FOR_QUAL_CREATION,
            ConversationState.QUAL_CREATED,
        ],
    )
    async def test_start_assistant_mode_succeeds_when_data_collection_complete(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        conversation_data: dict,
        conversation_repository_dep_with_autocommit,
        conversation_state: ConversationState,
    ):
        """Test that start_engagement_assistant_mode succeeds when data collection is complete."""
        # Create a conversation
        create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
        conversation_id = UUID(create_response.json()['conversation']['id'])

        # Set conversation state to data collection complete
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2024-01-01', '2024-12-31'),
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        await conversation_repository_dep_with_autocommit.update_confirmed_data_and_state(
            conversation_id, confirmed_data, conversation_state
        )

        # Attempt to start assistant mode
        assistant_url = url_resolver.reverse(
            operation_ids.conversation.ENGAGEMENT_CHAT_CREATE, conversation_id=conversation_id
        )
        assistant_data = StartAssistantModeRequest(page_type=PageType.ENGAGEMENT_DESCRIPTION)

        response = await async_client.post(assistant_url, headers=auth_header, json=assistant_data.model_dump())

        # Should succeed
        assert response.status_code == status.HTTP_200_OK
        # No specific response data to check for this endpoint's success, just status code

    @pytest.mark.parametrize(
        'conversation_state',
        [
            ConversationState.DATA_COMPLETE,
            ConversationState.READY_FOR_QUAL_CREATION,
            ConversationState.QUAL_CREATED,
        ],
    )
    async def test_get_combined_extracted_data_raises_error_when_data_collection_complete(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        conversation_data: dict,
        conversation_repository_dep_with_autocommit,
        conversation_state: ConversationState,
    ):
        """Test that get_combined_extracted_data raises DataCollectionCompleteError when data collection is complete."""
        # Create a conversation
        create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
        conversation_id = UUID(create_response.json()['conversation']['id'])

        # Set conversation state to data collection complete
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2024-01-01', '2024-12-31'),
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        await conversation_repository_dep_with_autocommit.update_confirmed_data_and_state(
            conversation_id, confirmed_data, conversation_state
        )

        # Attempt to get combined extracted data
        combined_data_url = url_resolver.reverse(
            operation_ids.extracted_data_summary.GET, conversation_id=conversation_id
        )
        with patch(
            'repositories.ldmf_countries.LDMFCountriesRepository.list',
            new_callable=AsyncMock,
            return_value=[CountryData(memberFirmId=2768, name='United States', id=37)],
        ):
            response = await async_client.get(combined_data_url, headers=auth_header)
            expectred_ldmf = {'memberFirmId': 2768, 'name': 'United States', 'id': 37}
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert not data['client_name']
            assert data['ldmf_country'] == expectred_ldmf
