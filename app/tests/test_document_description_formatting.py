"""Tests for document description formatting in system messages."""

from unittest.mock import AsyncMock, <PERSON><PERSON>
from uuid import uuid4

import pytest

from schemas import AggregatedData, ConfirmedData, ConversationData
from services.conversation_message import ConversationMessageService
from services.document import DocumentService


class TestDocumentDescriptionFormatting:
    """Test cases for document description formatting functionality."""

    @pytest.fixture
    def conversation_message_service(self):
        """Create a ConversationMessageService instance with mocked dependencies."""
        mock_document_service = AsyncMock()
        mock_document_service.format_document_description = Mock(
            side_effect=DocumentService.format_document_description
        )
        return ConversationMessageService(
            conversation_message_repository=AsyncMock(),
            conversation_repository=AsyncMock(),
            document_service=mock_document_service,
            document_db_repository=AsyncMock(),
            processing_message_repository=AsyncMock(),
            kx_dash_service=AsyncMock(),
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            date_validator_service=AsyncMock(),
            system_message_generation_service=AsyncMock(),
            translation_service=AsyncMock(),
            client_name_option_handler=AsyncMock(),
            ldmf_country_option_handler=AsyncMock(),
            dates_option_handler=AsyncMock(),
            kx_dash_task_option_handler=AsyncMock(),
        )

    def test_format_document_description_single_file(self):
        """Test formatting for a single document."""
        filenames = ['Cybersecurity SOW.pdf']
        result = DocumentService.format_document_description(filenames)
        assert result == 'a document related to "Cybersecurity SOW"'

    def test_format_document_description_two_files(self):
        """Test formatting for two documents."""
        filenames = ['Cybersecurity SOW.pdf', 'Fashion Backward Note.docx']
        result = DocumentService.format_document_description(filenames)
        assert result == 'documents related to "Cybersecurity SOW" and "Fashion Backward Note"'

    def test_format_document_description_three_files(self):
        """Test formatting for three documents."""
        filenames = ['Cybersecurity SOW.pdf', 'Fashion Backward Note.docx', 'Project Plan.xlsx']
        result = DocumentService.format_document_description(filenames)
        assert result == 'documents related to "Cybersecurity SOW", "Fashion Backward Note", and "Project Plan"'

    def test_format_document_description_four_files(self):
        """Test formatting for four documents."""
        filenames = ['Doc1.pdf', 'Doc2.docx', 'Doc3.xlsx', 'Doc4.pptx']
        result = DocumentService.format_document_description(filenames)
        assert result == 'documents related to "Doc1", "Doc2", "Doc3", and "Doc4"'

    def test_format_document_description_empty_list(self):
        """Test formatting for empty filename list."""
        filenames = []
        result = DocumentService.format_document_description(filenames)
        assert result == 'documents'

    def test_format_document_description_removes_extensions(self):
        """Test that file extensions are properly removed."""
        filenames = ['test.pdf', 'document.docx', 'spreadsheet.xlsx', 'presentation.pptx']
        result = DocumentService.format_document_description(filenames)
        assert result == 'documents related to "test", "document", "spreadsheet", and "presentation"'

    async def test_generate_client_confirmation_content_multiple_documents(self, conversation_message_service):
        """Test that client confirmation content properly handles multiple documents."""
        # Mock dependencies
        conversation_message_service.document_db_repository.get_filenames_for_message = AsyncMock(
            return_value=['Cybersecurity SOW.pdf', 'Fashion Backward Note.docx']
        )
        conversation_message_service.extracted_data_service.single_value_validation = AsyncMock(
            return_value=AsyncMock(system_reply_type=None)
        )
        conversation_message_service.get_suggested_prompts = AsyncMock(return_value=[])

        # Create test data
        conversation_data = ConversationData(
            aggregated_data=AggregatedData(
                client_name=['FashionBackward'],
                ldmf_country=[],
                date_intervals=[],
                objective_and_scope=None,
                outcomes=None,
            ),
            confirmed_data=ConfirmedData(),
            conversation=AsyncMock(PublicId=uuid4()),
            conversation_message_history=[],
        )

        last_message = AsyncMock(id=uuid4())
        token = 'test_token'

        # Call the method
        result = await conversation_message_service._generate_client_confirmation_content(
            conversation_data, last_message, token
        )

        # Verify the result contains the expected multiple document description
        expected_description = 'documents related to "Cybersecurity SOW" and "Fashion Backward Note"'
        assert expected_description in result.formatted_content

    async def test_generate_client_confirmation_content_single_document(self, conversation_message_service):
        """Test that client confirmation content properly handles single document."""
        # Mock dependencies
        conversation_message_service.document_db_repository.get_filenames_for_message = AsyncMock(
            return_value=['Cybersecurity SOW.pdf']
        )
        conversation_message_service.extracted_data_service.single_value_validation = AsyncMock(
            return_value=AsyncMock(system_reply_type=None)
        )
        conversation_message_service.get_suggested_prompts = AsyncMock(return_value=[])

        # Create test data
        conversation_data = ConversationData(
            aggregated_data=AggregatedData(
                client_name=['FashionBackward'],
                ldmf_country=[],
                date_intervals=[],
                objective_and_scope=None,
                outcomes=None,
            ),
            confirmed_data=ConfirmedData(),
            conversation=AsyncMock(PublicId=uuid4()),
            conversation_message_history=[],
        )

        last_message = AsyncMock(id=uuid4())
        token = 'test_token'

        # Call the method
        result = await conversation_message_service._generate_client_confirmation_content(
            conversation_data, last_message, token
        )

        # Verify the result contains the expected single document description
        expected_description = 'a document related to "Cybersecurity SOW"'
        assert expected_description in result.formatted_content

    async def test_generate_client_confirmation_content_no_documents(self, conversation_message_service):
        """Test that client confirmation content handles no documents gracefully."""
        # Mock dependencies
        conversation_message_service.document_db_repository.get_filenames_for_message = AsyncMock(return_value=[])
        conversation_message_service.extracted_data_service.single_value_validation = AsyncMock(
            return_value=AsyncMock(system_reply_type=None)
        )
        conversation_message_service.get_suggested_prompts = AsyncMock(return_value=[])

        # Create test data
        conversation_data = ConversationData(
            aggregated_data=AggregatedData(
                client_name=['FashionBackward'],
                ldmf_country=[],
                date_intervals=[],
                objective_and_scope=None,
                outcomes=None,
            ),
            confirmed_data=ConfirmedData(),
            conversation=AsyncMock(PublicId=uuid4()),
            conversation_message_history=[],
        )

        last_message = AsyncMock(id=uuid4())
        token = 'test_token'

        # Call the method
        result = await conversation_message_service._generate_client_confirmation_content(
            conversation_data, last_message, token
        )

        # Verify the result uses the fallback template without document description
        assert 'FashionBackward' in result.formatted_content
        # Should not contain document description when no files
        assert 'document' not in result.formatted_content.lower() or 'documents' not in result.formatted_content.lower()
