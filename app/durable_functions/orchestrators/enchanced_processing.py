"""Enhanced processing orchestrator for Azure Durable Functions.

This module contains the main orchestrator and sub-orchestrator for enhanced
data processing, implementing a fan-out/fan-in pattern with progress tracking.
"""

import logging
from typing import Any

import azure.durable_functions as df
from azure.durable_functions import DurableOrchestrationContext

from constants.durable_functions import ActivityName, En<PERSON>tyOperation, EventType, OrchestratorName
from constants.extracted_data import DataSourceType
from constants.prompt import prompt_templates
from durable_functions.activities.models import (
    EnhancedExtractionActivityInput,
    EnhancedExtractionActivityOutput,
    FieldExtractionTask,
    ListBlobsActivityInput,
    ListBlobsActivityOutput,
    SaveExtractionDataActivityInput,
    SendNotificationActivityInput,
    SummarizeOtherFieldActivityInput,
    SummarizeOtherFieldActivityOutput,
)

from .models import EnhancedProcessingInput


logger = logging.getLogger(__name__)
bp = df.Blueprint()


# Constants for better maintainability
PROCESSING_STEPS_PER_BLOB = 3  # summarize, extract, save


def _create_extraction_tasks(summarized_content: str) -> list[FieldExtractionTask]:
    """Create a list of field extraction tasks based on summarized content."""
    return [
        FieldExtractionTask(
            field_name='engagement_summary',
            context=summarized_content,
            system_prompt='Extract the engagement_summary from the context.',
            user_prompt=f'Context: {summarized_content}',
        ),
        FieldExtractionTask(
            field_name='one_line_description',
            context=summarized_content,
            system_prompt='Extract the one_line_description from the context.',
            user_prompt=f'Context: {summarized_content}',
        ),
        FieldExtractionTask(
            field_name='client_references',
            context=summarized_content,
            system_prompt=prompt_templates.extract_client_references.SYSTEM,
            user_prompt=prompt_templates.extract_client_references.USER.format(input=summarized_content),
        ),
        FieldExtractionTask(
            field_name='client_name_sharing',
            context=summarized_content,
            system_prompt=prompt_templates.extract_client_name_sharing.SYSTEM,
            user_prompt=prompt_templates.extract_client_name_sharing.USER.format(input=summarized_content),
        ),
        FieldExtractionTask(
            field_name='qual_usage',
            context=summarized_content,
            system_prompt=prompt_templates.extract_qual_usage.SYSTEM,
            user_prompt=prompt_templates.extract_qual_usage.USER.format(input=summarized_content),
        ),
    ]


def _create_progress_notification_input(
    signalr_user_id: str, message_id: str, progress_percent: int
) -> SendNotificationActivityInput:
    """Create a progress notification input for SignalR."""
    return SendNotificationActivityInput(
        event_type=EventType.DraftQualProgress,
        data={'percent': progress_percent, 'message_id': message_id},
        signalr_user_id=signalr_user_id,
    )


def _send_progress_update(context: DurableOrchestrationContext, entity_id: df.EntityId, signalr_user_id: str | None):
    """Helper function to increment progress and send notifications.

    Args:
        context: The durable orchestration context.
        entity_id: The entity ID for the progress tracker.
        signalr_user_id: The SignalR user ID for notifications.
    """
    if signalr_user_id:
        progress_data: dict[str, Any] = yield context.call_entity(entity_id, EntityOperation.INCREMENT)
        if progress_data:
            percent = progress_data.get('percent', 0)
            all_message_ids = progress_data.get('message_ids', [])
            # Send notification for only the first message ID to avoid duplicates
            if all_message_ids:
                yield context.call_activity(
                    ActivityName.SendNotification,
                    _create_progress_notification_input(signalr_user_id, all_message_ids[0], percent),
                )


@bp.orchestration_trigger('context', OrchestratorName.EnchancedProcessingSubOrchestrator)
def process_message(context: DurableOrchestrationContext):
    """Orchestrate the enhanced processing for a single message ID.

    This function processes a single message by listing its associated blobs,
    processing each one, and signaling a central entity to update the overall progress.

    Args:
        context: The durable orchestration context containing input data.
    """
    input_dict = context.get_input()
    if not input_dict:
        logger.error('No input provided for enhanced processing sub-orchestrator')
        return

    try:
        message_id = input_dict['message_id']
        signalr_user_id = input_dict.get('signalr_user_id')
        entity_id_name = input_dict['entity_id_name']
        entity_id_key = input_dict['entity_id_key']
        entity_id = df.EntityId(entity_id_name, entity_id_key)
    except KeyError as e:
        logger.error(f'Missing required input field: {e}')
        return

    prefix = f'chunks_extracted/{message_id}/'.lower()
    blobs_outputs: list[ListBlobsActivityOutput] = yield context.call_activity(
        ActivityName.ListBlobs, ListBlobsActivityInput(prefix=prefix)
    )

    for blob_output in blobs_outputs:
        if not blob_output.chunks:
            continue

        # Step 1: Summarize chunks
        summarize_input = SummarizeOtherFieldActivityInput(
            conversation_id=message_id, chunks_extracted=blob_output.chunks
        )
        summarized_result: SummarizeOtherFieldActivityOutput = yield context.call_activity(
            ActivityName.SummarizeOtherField, summarize_input
        )
        # Progress update after summarization
        yield from _send_progress_update(context, entity_id, signalr_user_id)

        if summarized_result and summarized_result.summarized_other_content:
            # Step 2: Enhanced extraction
            tasks = _create_extraction_tasks(summarized_result.summarized_other_content)
            enhanced_extraction_input = EnhancedExtractionActivityInput(
                conversation_id=message_id, tasks=tasks, source=blob_output.source
            )
            enhanced_extraction_result: EnhancedExtractionActivityOutput = yield context.call_activity(
                ActivityName.EnhancedExtraction, enhanced_extraction_input
            )
            # Progress update after extraction
            yield from _send_progress_update(context, entity_id, signalr_user_id)

            if enhanced_extraction_result and enhanced_extraction_result.extracted_data:
                # Step 3: Save extraction data
                save_extraction_input = SaveExtractionDataActivityInput(
                    message_id=message_id,
                    extraction_data=enhanced_extraction_result.extracted_data,
                    data_source_type=DataSourceType(blob_output.source),
                )
                save_extraction_data_output = yield context.call_activity(
                    ActivityName.SaveExtractionData, save_extraction_input
                )
                # Progress update after saving
                yield from _send_progress_update(context, entity_id, signalr_user_id)
                if save_extraction_data_output.replaced_confirmed_fields:
                    logger.warning(
                        f'Replaced confirmed fields in enchanced_processing_orchestrator: {save_extraction_data_output.replaced_confirmed_fields}'
                    )
            else:
                # If no extraction result, still increment progress for the save step
                yield from _send_progress_update(context, entity_id, signalr_user_id)
        else:
            # If no summarized content, still increment progress for extraction and save steps
            yield from _send_progress_update(context, entity_id, signalr_user_id)  # extraction step
            yield from _send_progress_update(context, entity_id, signalr_user_id)  # save step


def _validate_orchestrator_input(input_dict: dict[str, Any] | None, orch_name: str) -> EnhancedProcessingInput | None:
    """Validate and parse the orchestrator input data.

    Args:
        input_dict: Raw input dictionary from the orchestration context.
        orch_name: Name of the orchestrator for logging purposes.

    Returns:
        Parsed input data or None if validation fails.
    """
    if not input_dict:
        logger.error(f'No input provided for {orch_name}')
        return None

    try:
        input_data = EnhancedProcessingInput.model_validate(input_dict)
    except Exception as e:
        logger.error(f'Invalid input data for {orch_name}: {e}')
        return None

    if not input_data.message_ids:
        logger.error(f'No message IDs provided for {orch_name}')
        return None

    return input_data


def _send_initial_progress_notification(context: DurableOrchestrationContext, signalr_user_id: str, message_id: str):
    """Send initial 0% progress notification.

    Args:
        context: The durable orchestration context.
        signalr_user_id: The SignalR user ID for notifications.
        message_id: The message ID to use for the notification.
    """
    yield context.call_activity(
        ActivityName.SendNotification,
        _create_progress_notification_input(signalr_user_id, message_id, 0),
    )


def _calculate_total_processing_steps(context: DurableOrchestrationContext, message_ids: list[str]):
    """Calculate the total number of blobs and processing steps.

    Args:
        context: The durable orchestration context.
        message_ids: List of message IDs to process.

    Returns:
        Tuple of (total_blobs, total_processing_steps).
    """
    list_blob_tasks = [
        context.call_activity(
            ActivityName.ListBlobs,
            ListBlobsActivityInput(prefix=f'chunks_extracted/{message_id}/'.lower()),
        )
        for message_id in message_ids
    ]
    all_blob_outputs: list[list[ListBlobsActivityOutput]] = yield context.task_all(list_blob_tasks)
    total_blobs = sum(len(blobs) for blobs in all_blob_outputs)
    total_processing_steps = total_blobs * PROCESSING_STEPS_PER_BLOB

    return total_blobs, total_processing_steps


def _initialize_progress_tracker(
    context: DurableOrchestrationContext, total_processing_steps: int, message_ids: list[str]
):
    """Initialize the progress tracker entity.

    Args:
        context: The durable orchestration context.
        total_processing_steps: Total number of processing steps.
        message_ids: List of message IDs being processed.

    Returns:
        The entity ID for the progress tracker.
    """
    entity_id = df.EntityId('progress_tracker_entity', context.instance_id)
    yield context.call_entity(
        entity_id,
        EntityOperation.INITIALIZE,
        {'total_items': total_processing_steps, 'message_ids': message_ids},
    )
    return entity_id


def _execute_parallel_processing(
    context: DurableOrchestrationContext, message_ids: list[str], signalr_user_id: str | None, entity_id: df.EntityId
):
    """Execute parallel processing using sub-orchestrators.

    Args:
        context: The durable orchestration context.
        message_ids: List of message IDs to process.
        signalr_user_id: The SignalR user ID for notifications.
        entity_id: The entity ID for the progress tracker.
    """
    processing_tasks = [
        context.call_sub_orchestrator(
            OrchestratorName.EnchancedProcessingSubOrchestrator,
            {
                'message_id': message_id,
                'signalr_user_id': signalr_user_id,
                'entity_id_name': entity_id.name,
                'entity_id_key': entity_id.key,
            },
        )
        for message_id in message_ids
    ]
    yield context.task_all(processing_tasks)


def _send_final_progress_notification(context: DurableOrchestrationContext, signalr_user_id: str, message_id: str):
    """Send final 100% progress notification.

    Args:
        context: The durable orchestration context.
        signalr_user_id: The SignalR user ID for notifications.
        message_id: The message ID to use for the notification.
    """
    yield context.call_activity(
        ActivityName.SendNotification,
        _create_progress_notification_input(signalr_user_id, message_id, 100),
    )


@bp.orchestration_trigger(context_name='context', orchestration=OrchestratorName.EnchancedExtraction)
def enchanced_processing_orchestrator(context: DurableOrchestrationContext):
    """Main orchestrator for enhanced data processing.

    This orchestrator implements a fan-out/fan-in pattern to process multiple
    message IDs in parallel, using a Durable Entity to track overall progress.

    The orchestrator performs the following steps:
    1. Validates input data
    2. Sends initial progress notification (0%)
    3. Calculates total processing steps
    4. Initializes progress tracker entity
    5. Executes parallel processing via sub-orchestrators
    6. Cleans up progress tracker
    7. Sends final progress notification (100%)

    Args:
        context: The durable orchestration context containing input data.
    """
    orch_name = str(OrchestratorName.EnchancedExtraction)
    input_dict = context.get_input()

    # Validate input data
    input_data = _validate_orchestrator_input(input_dict, orch_name)
    if not input_data:
        return

    # Send initial progress notification
    if input_data.signalr_user_id:
        yield from _send_initial_progress_notification(context, input_data.signalr_user_id, input_data.message_ids[0])

    try:
        # Calculate total processing steps
        total_blobs, total_processing_steps = yield from _calculate_total_processing_steps(
            context, input_data.message_ids
        )

        if total_blobs == 0:
            logger.info('No blobs to process.')
        else:
            # Initialize progress tracker and execute processing
            entity_id = yield from _initialize_progress_tracker(context, total_processing_steps, input_data.message_ids)

            yield from _execute_parallel_processing(
                context, input_data.message_ids, input_data.signalr_user_id, entity_id
            )

            # Clean up progress tracker
            context.signal_entity(entity_id, EntityOperation.RESET)

        # Send final progress notification
        if input_data.signalr_user_id:
            yield from _send_final_progress_notification(
                context, input_data.signalr_user_id, input_data.message_ids[-1]
            )

        logger.info(f'Successfully completed {orch_name} for all message IDs.')

    except Exception:
        logger.exception(f'Error in {orch_name} orchestrator')
