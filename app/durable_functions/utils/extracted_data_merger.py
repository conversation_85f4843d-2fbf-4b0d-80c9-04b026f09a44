import re

from constants.extracted_data import COMPANY_SUFFIXES, DataSourceType
from durable_functions.utils.models import Engagement<PERSON>eri<PERSON>, FinalExtractionDataResults, LLMExtractedDataResult


class ExtractedDataMerger:
    """
    This class contains methods to merge the results of the text extraction.
    """

    CLIENT_NAME_PATTERN = re.compile(
        f'({"|".join([rf"{suffix}.?$" for suffix in COMPANY_SUFFIXES])})', flags=re.IGNORECASE
    )

    @classmethod
    def merge_results(cls, results: list[LLMExtractedDataResult]) -> FinalExtractionDataResults:
        final_result = FinalExtractionDataResults()
        has_more_than_two_dates = False

        for result in results:
            final_result.client_names = cls._merge_client_names(result.client_name, final_result.client_names)
            final_result.lead_member_countries = cls._merge_strings(
                result.ldmf_country, final_result.lead_member_countries, split_by='&'
            )
            final_result.objective_and_scope = cls._merge_by_longest_string(
                result.objective_and_scope, final_result.objective_and_scope
            )
            final_result.outcomes = cls._merge_by_longest_string(result.outcomes, final_result.outcomes)
            final_result.periods = cls._merge_periods(
                result.engagement_start_date,
                result.engagement_end_date,
                final_result.periods,
            )
            final_result.periods_original = cls._merge_periods(
                result.engagement_start_date_original,
                result.engagement_end_date_original,
                final_result.periods_original,
            )

            # Check if any result has multiple dates
            if result.more_than_two_dates:
                has_more_than_two_dates = True

        # After the loop, filter out "Deloitte" from client_names
        if final_result.client_names:
            final_result.client_names = [name for name in final_result.client_names if name.lower() != 'deloitte']

        # Set more_than_two_dates flag in the final result
        final_result.more_than_two_dates = has_more_than_two_dates

        return final_result

    @staticmethod
    def _merge_strings(
        current_string: str | None, all_strings: list[str] | None, split_by: str = ','
    ) -> list[str] | None:
        # Split current_string by comma and strip whitespace
        if current_string:
            split_items = [item.strip() for item in current_string.split(split_by) if item.strip()]
        else:
            split_items = []

        # Initialize all_strings if needed
        if all_strings is None:
            all_strings = []

        # Add unique items (case-insensitive)
        existing_lower = {item.lower() for item in all_strings}
        for item in split_items:
            if item.lower() not in existing_lower:
                all_strings.append(item)
                existing_lower.add(item.lower())

        return all_strings if all_strings else None

    @staticmethod
    def _merge_by_longest_string(current_text: str | None, final_text: str | None) -> str | None:
        return max(filter(None, (current_text, final_text)), key=len, default=None)

    @staticmethod
    def _merge_periods(
        start_date: str | None,
        end_date: str | None,
        periods: list[EngagementPeriod] | None,
    ) -> list[EngagementPeriod] | None:
        if not start_date and not end_date:
            return periods

        current_period = EngagementPeriod(start_date=start_date, end_date=end_date)
        if not periods:
            return [current_period]

        if current_period not in periods:
            periods.append(current_period)
        return periods

    @classmethod
    def merge_multi_source_results(
        cls, source_results: list[tuple[DataSourceType, FinalExtractionDataResults]]
    ) -> FinalExtractionDataResults:
        """
        Merge extraction results from multiple sources with different aggregation strategies.

        Args:
            source_results: List of tuples containing (source_type, extraction_results)

        Returns:
            FinalExtractionDataResults with aggregated data
        """
        final_result = FinalExtractionDataResults()
        qual_usage_values = set()  # Track all unique non-undefined qual_usage values

        for _, result in source_results:
            # Client names: Extend/merge unique values across all sources
            final_result.client_names = cls._merge_unique_lists(result.client_names, final_result.client_names)

            # LDMF countries: Extend/merge unique values across all sources
            final_result.lead_member_countries = cls._merge_unique_lists(
                result.lead_member_countries, final_result.lead_member_countries
            )

            # Date periods: Extend/collect date intervals as pairs across all sources
            final_result.periods = cls._merge_periods_lists(result.periods, final_result.periods)
            final_result.periods_original = cls._merge_periods_lists(
                result.periods_original, final_result.periods_original
            )

            # Other fields: Concatenate values for now (can be enhanced later)
            final_result.objective_and_scope = cls._concatenate_text_fields(
                result.objective_and_scope, final_result.objective_and_scope
            )
            final_result.outcomes = cls._concatenate_text_fields(result.outcomes, final_result.outcomes)

            # Collect qual_usage values for Enhanced Extraction conflict detection
            if result.qual_usage and result.qual_usage != 'undefined':
                qual_usage_values.add(result.qual_usage)

        # Handle qual_usage conflicts and defaults for Enhanced Extraction
        if not qual_usage_values or (qual_usage_values and len(qual_usage_values) > 1):
            # Multiple different qual_usage values found - default to 'approval' for safety
            final_result.qual_usage = 'approval'
        else:
            final_result.qual_usage = qual_usage_values.pop()

        return final_result

    @staticmethod
    def _merge_unique_lists(current_list: list[str] | None, final_list: list[str] | None) -> list[str] | None:
        """Merge two lists, keeping only unique values (case-insensitive)."""
        if not current_list and not final_list:
            return None
        if not current_list:
            return final_list
        if not final_list:
            return current_list

        # Combine lists and remove duplicates (case-insensitive)
        combined = final_list.copy()
        for item in current_list:
            if item.lower() not in [existing.lower() for existing in combined]:
                combined.append(item)
        return combined

    @staticmethod
    def _merge_periods_lists(
        current_periods: list[EngagementPeriod] | None, final_periods: list[EngagementPeriod] | None
    ) -> list[EngagementPeriod] | None:
        """Merge two lists of engagement periods, avoiding duplicates."""
        if not current_periods and not final_periods:
            return None
        if not current_periods:
            return final_periods
        if not final_periods:
            return current_periods

        # Combine periods and remove duplicates
        combined = final_periods.copy()
        for period in current_periods:
            if period not in combined:
                combined.append(period)
        return combined

    @staticmethod
    def _concatenate_text_fields(
        current_text: str | None,
        final_text: str | None,
    ) -> str | None:
        """Concatenate text fields with source type labels."""
        if not current_text and not final_text:
            return None
        if not current_text:
            return final_text
        if not final_text:
            return f'{current_text}'

        return f'{final_text}\n\n {current_text}'

    @classmethod
    def _merge_client_names(cls, client_name: str | None, all_client_names: list[str] | None) -> list[str] | None:
        # Split client_name by comma and strip whitespace
        if client_name:
            client_names = [stripped for item in client_name.split(',') if (stripped := item.strip())]
        else:
            client_names = []

        # Initialize all_client_names if needed
        all_client_names = all_client_names or []

        existing_lower = {item.lower() for item in all_client_names}
        for client_name in client_names:
            # Check if the client name is a company suffix and append it to the last client name
            if all_client_names and cls.CLIENT_NAME_PATTERN.match(client_name):
                existing_lower.remove(all_client_names[-1].lower())
                all_client_names[-1] = ', '.join([all_client_names[-1], client_name])
                existing_lower.add(all_client_names[-1].lower())
            elif client_name.lower() not in existing_lower:
                all_client_names.append(client_name)
                existing_lower.add(client_name.lower())

        return all_client_names or None
