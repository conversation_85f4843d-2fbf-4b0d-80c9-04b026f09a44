from abc import ABC, abstractmethod
from uuid import UUID

from fastapi import UploadFile

from schemas import CombinedMessageSerializer, Option


class MessageHandler(ABC):
    @abstractmethod
    async def handle(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer: ...
