import logging
from uuid import UUI<PERSON>

from fastapi import UploadFile

from constants.message import MessageR<PERSON>, MessageType, SystemReplyType
from repositories import ConversationMessageRepository
from schemas import CombinedMessageSerializer, MessageValidator, Option, SystemMessageSerializer, UserMessageSerializer
from services.message_handlers.base import MessageHandler


__all__ = ['EngagementDescriptionMessageHandler']

logger = logging.getLogger(__name__)


class EngagementDescriptionMessageHandler(MessageHandler):
    def __init__(self, conversation_message_repository: ConversationMessageRepository):
        self.conversation_message_repository = conversation_message_repository

    async def handle(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=content,
            selected_option=selected_option,
            files=files,
            system_reply_type=None,
        )
        user_message = await self.conversation_message_repository.create(user_message_to_persist)

        system_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Thank you for providing the engagement description.',
            system_reply_type=SystemReplyType.BRIEF_DESCRIPTION,
        )
        system_message = await self.conversation_message_repository.create(system_message_to_persist)

        return CombinedMessageSerializer(
            user=UserMessageSerializer.model_validate(user_message),
            system=SystemMessageSerializer.model_validate(system_message),
        )
