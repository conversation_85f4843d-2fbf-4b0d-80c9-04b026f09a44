import logging
from typing import TYPE_CHECKING, cast
from uuid import UUID

from fastapi import UploadFile

from config import settings
from constants.durable_functions import ProcessingStatus
from constants.extracted_data import (
    ConversationState,
    MissingDataStatus,
    RequiredField,
)
from constants.message import (
    THANX_FOR_INFORMATION,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    OptionType,
    SuggestedUserPrompt,
    SystemReplyType,
)
from exceptions import ConfirmedDataReplacementError
from exceptions.conversation import ConversationDataInconsistencyError
from exceptions.entity import EntityNotFoundError
from models import QualConversation
from schemas import (
    CombinedMessageSerializer,
    ConversationMessageProcessingResult,
    DocumentCreationRequest,
    KXDashTaskOption,
    LDMFCountryOption,
    MessageValidator,
    Option,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from services.message_handlers.base import MessageHandler
from services.message_processor import ConversationMessageProcessor
from services.option_handlers import (
    ClientNameOptionHandlerService,
    DatesOptionHandlerService,
    KXDashTaskOptionHandlerService,
    LDMFCountryOptionHandlerService,
)
from services.proactive_chat import ProactiveChatService


if TYPE_CHECKING:
    from app.services.conversation_message import ConversationMessageService


__all__ = ['PromptMessageHandler']

logger = logging.getLogger(__name__)


class PromptMessageHandler(MessageHandler):
    _EMPTY_LDMF_COUNTRY_OPTION = [LDMFCountryOption(type=OptionType.LDMF_COUNTRY, ldmf_country='')]

    def __init__(
        self,
        conversation_message_service: 'ConversationMessageService',
        client_name_option_handler: ClientNameOptionHandlerService,
        ldmf_country_option_handler: LDMFCountryOptionHandlerService,
        dates_option_handler: DatesOptionHandlerService,
        kx_dash_task_option_handler: KXDashTaskOptionHandlerService,
    ):
        self.conversation_message_service = conversation_message_service
        self.conversation_message_repository = conversation_message_service.conversation_message_repository
        self.conversation_repository = conversation_message_service.conversation_repository
        self.document_service = conversation_message_service.document_service
        self.document_db_repository = conversation_message_service.document_db_repository
        self.processing_message_repository = conversation_message_service.processing_message_repository
        self.kx_dash_service = conversation_message_service.kx_dash_service
        self.translation_service = conversation_message_service.translation_service
        self.intent_classifier_service = conversation_message_service.intent_classifier_service
        self.extracted_data_service = conversation_message_service.extracted_data_service
        self.date_validator_service = conversation_message_service.date_validator_service
        self.system_message_service = conversation_message_service.system_message_service

        # Option handlers
        self.client_name_option_handler = client_name_option_handler
        self.ldmf_country_option_handler = ldmf_country_option_handler
        self.dates_option_handler = dates_option_handler
        self.kx_dash_task_option_handler = kx_dash_task_option_handler

    async def handle(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        await self._ensure_previous_message_processed(conversation_id)

        content = content.strip()
        if files:
            message_type = MessageType.TEXT_WITH_FILE if content else MessageType.FILE
        else:
            message_type = MessageType.TEXT

        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=message_type,
            content=content,
            selected_option=selected_option,
            files=files,
            system_reply_type=None,
        )

        user_message = await self.conversation_message_service.create_message(user_message_to_persist)
        user_message = cast(UserMessageSerializer, user_message)

        conversation_data = None
        intention = None
        system_reply_type = None

        if selected_option:
            try:
                match selected_option.type:
                    case OptionType.CLIENT_NAME:
                        system_message = await self.client_name_option_handler.handle(
                            selected_option, conversation_id, token
                        )
                    case OptionType.LDMF_COUNTRY:
                        system_message = await self.ldmf_country_option_handler.handle(
                            selected_option, conversation_id, token
                        )
                    case OptionType.DATES:
                        system_message = await self.dates_option_handler.handle(selected_option, conversation_id, token)
                    case OptionType.KX_DASH_TASK:
                        system_message = await self.kx_dash_task_option_handler.handle(
                            selected_option, conversation_id, token
                        )
                    case _:
                        raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
            except ConfirmedDataReplacementError:
                logger.info(
                    'ConfirmedDataReplacementError detected in %s for selected option %s',
                    'ConversationMessageService.create',
                    selected_option.type,
                )
                system_message = (
                    await self.conversation_message_service._handle_confirmed_fields_change_prohibited_message(
                        conversation_id
                    )
                )
                return CombinedMessageSerializer(
                    user=user_message, system=cast(SystemMessageSerializer, system_message)
                )
        else:
            # Get conversation message history for message processor & suggested prompts tracking.
            conversation_data = await self.conversation_message_service._fetch_conversation_data(conversation_id, token)
            conversation_message_history = conversation_data.conversation_message_history

            # Process the user message to get the system reply and intention
            if content:
                translated_content = await self.translation_service.get_translated_text(content)
                if translated_content != user_message.content:
                    # If the message.content was translated, update message
                    await self.conversation_message_service.update_message_fields(
                        user_message.id,
                        {
                            'Translation': translated_content,
                        },
                    )
                    user_message.translation = translated_content
            message_processor = ConversationMessageProcessor(
                conversation_id=conversation_id,
                conversation_data=conversation_data,
                user_message=user_message,
                conversation_message_history=conversation_message_history,
                intent_classifier_service=self.intent_classifier_service,
                extracted_data_service=self.extracted_data_service,
                conversation_repository=self.conversation_repository,
                conversation_message_repository=self.conversation_message_repository,
                date_validator_service=self.date_validator_service,
                document_service=self.document_service,
                token=token,
            )
            try:
                message_processing_result: ConversationMessageProcessingResult = await message_processor.run()
            except ConfirmedDataReplacementError:
                logger.info(
                    'ConfirmedDataReplacementError detected in %s',
                    'ConversationMessageProcessor.run',
                )
                system_message = (
                    await self.conversation_message_service._handle_confirmed_fields_change_prohibited_message(
                        conversation_id
                    )
                )
                return CombinedMessageSerializer(
                    user=user_message, system=cast(SystemMessageSerializer, system_message)
                )

            system_reply_type = message_processing_result.system_reply_type
            intention = message_processing_result.intention
            user_message_to_persist.intention = intention

            # Get aggregated extracted and confirmed data after message processing.
            conversation_data = await self.conversation_message_service._fetch_conversation_data(conversation_id, token)

            # Get suggested prompts
            suggested_prompts = await self.conversation_message_service.get_suggested_prompts(
                conversation_id=conversation_id,
                conversation_data=conversation_data,
                user_message=user_message,
                files=files,
                intention=intention,
                current_reply_type=system_reply_type,
                called_from='ConversationMessageService.create.no_options_flow',
            )

            # Update the user message with the determined intention
            await self.conversation_message_service.update_message_fields(user_message.id, {'Intention': intention})

            dash_discard_intentions = {
                ConversationMessageIntention.DASH_DISCARD,
                ConversationMessageIntention.USER_DENIAL,
                ConversationMessageIntention.GENERATE_QUAL,
            }

            if intention in dash_discard_intentions and system_reply_type == SystemReplyType.WELCOME_MESSAGE:
                dash_discard_message = await self.conversation_message_service._get_dash_discard_response(
                    user_message, message_processing_result, suggested_prompts
                )
                return dash_discard_message

            system_message = None
            should_generate_system_message = not (
                (content and intention == ConversationMessageIntention.EXTRACTION) or files
            )
            if should_generate_system_message:
                system_message = await self.conversation_message_service._get_system_message_with_result(
                    user_message_to_persist,
                    message_processing_result,
                    suggested_prompts,
                    conversation_data.confirmed_data,
                )
                if not system_message.content:
                    system_message = None

        if not conversation_data:
            conversation_data = await self.conversation_message_service._fetch_conversation_data(conversation_id, token)

        # Append enriched message to system message
        if system_message:
            confirmed_data = conversation_data.confirmed_data
            is_dash_task = isinstance(selected_option, KXDashTaskOption)
            proactive_chat_service = ProactiveChatService(
                conversation_data=conversation_data,
                system_reply_type=system_message.system_reply_type,
                is_dash_task=is_dash_task,
            )

            if is_dash_task:
                await self.conversation_message_service.autoconfirm_data_from_kxdash(
                    conversation_data, conversation_id, token
                )

            # Get proactive message content
            proactive_message_content, proactive_system_reply = proactive_chat_service.get_proactive_system_message()
            missing_data_response = None
            # Get proactive options
            if (
                proactive_message_content
                and not system_message.options
                and system_message.system_reply_type
                not in [
                    SystemReplyType.UNDEFINED,
                    SystemReplyType.BRIEF_DESCRIPTION,
                    SystemReplyType.EXAMPLE,
                    SystemReplyType.CLIENT_NOT_FOUND,
                    SystemReplyType.CLIENT_NAME_TOO_LONG,
                    SystemReplyType.DASH_TASK_SELECTED_TEMPLATE,
                ]
            ):
                missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                    conversation_id=conversation_id,
                    token=token,
                    confirmed_data=confirmed_data,
                    called_from='ConversationMessageService.create',
                    conversation_data=conversation_data,
                )
                missing_eng_dates = missing_data_response.next_expected_field == RequiredField.ENGAGEMENT_DATES
                proactive_options = self.conversation_message_service._convert_to_option_objects(
                    missing_data_response.options, missing_data_response.conversation_state
                )
                changed_ldmf_option = self.extracted_data_service.catch_invalid_ldmf_option(
                    missing_data_response, missing_data_response.conversation_state, system_reply_type
                )
                status_is_missing = missing_data_response.status == MissingDataStatus.MISSING_DATA
                is_proactive_options_available = proactive_options and len(proactive_options) > 1
                is_ldmf_suggestion_used = SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM == content

                if changed_ldmf_option and not is_proactive_options_available and not is_ldmf_suggestion_used:
                    # The message from missing_data_response might contain a generic prompt that needs to be cleared
                    # if the system_message.content already contains a specific LDMF question.
                    ldmf_search_text = 'Could you tell me who the Lead Deloitte Member Firm is for this engagement?'
                    if missing_data_response.message and ldmf_search_text in system_message.content:
                        missing_data_response.message = ''
                    if status_is_missing:
                        missing_data_response.message = SystemReplyType.EXTRACTED_LDMF_NOT_VALID.message_text
                    system_message.content = f'{system_message.content}\n\n{missing_data_response.message}'
                    system_message.options = [changed_ldmf_option]
                elif missing_eng_dates:
                    system_message.content = proactive_message_content
                    system_message.options = proactive_options
                elif (
                    is_ldmf_suggestion_used or intention == ConversationMessageIntention.USER_DENIAL
                ) and changed_ldmf_option:
                    system_message.content = system_message.content
                    system_message.options = [changed_ldmf_option]
                else:
                    if proactive_system_reply:
                        system_message.system_reply_type = proactive_system_reply
                    system_message.content = f'{system_message.content}\n\n{proactive_message_content}'
                    system_message.options = proactive_options

                if system_message.system_reply_type == SystemReplyType.NEED_INFO_CLIENT_NAME:
                    if proactive_chat_service.confirmed_fields_names:
                        system_message.content = f'{THANX_FOR_INFORMATION}\n{system_message.content}'

            else:
                # custom proactive messages for ConversationState.DATA_COMPLETE
                if system_message.system_reply_type in SystemReplyType.get_data_confirmation_replies():
                    conversation_data = await self.conversation_message_service._fetch_conversation_data(
                        conversation_id, token
                    )
                    conversation: QualConversation = conversation_data.conversation

                    # Clear last_confirmed_field to prevent showing confirmation messages repeatedly
                    await self.conversation_message_service._clear_last_confirmed_field(conversation_id)

                    if str(conversation.State) == ConversationState.DATA_COMPLETE.value:
                        # same logic as in ConversationMessageProcessor._handle_data_complete_response
                        reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                        system_message.content = f'{system_message.content} {reply_type.message_text}'
                        system_message.system_reply_type = reply_type
                        system_message.suggested_prompts = [
                            SuggestedUserPrompt.NO_CREATE_MY_QUAL.value,
                        ]

            conversation_collects_country = (
                missing_data_response.conversation_state == ConversationState.COLLECTING_COUNTRY
                if missing_data_response
                else False
            )
            client_selected = selected_option and selected_option.type == OptionType.CLIENT_NAME
            if client_selected and not conversation_collects_country:
                # may modify system_message.options
                await self.conversation_message_service._handle_ldmf_country_options_after_client_name_confirmation(
                    system_message, proactive_chat_service, conversation_data
                )

            # Add suggested prompts when original system message content was None or empty
            not_intentions = [
                ConversationMessageIntention.CHANGE_ENGAGEMENT_DATES,
                ConversationMessageIntention.USER_DENIAL,
            ]
            if not system_message.suggested_prompts and intention not in not_intentions:
                # method possibly modifies system_message.suggested_prompts and system_message.system_reply_type
                await self.conversation_message_service._generate_and_set_suggested_prompts(
                    system_message, conversation_id, user_message, files, conversation_data
                )

        # Prepare & return response
        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await self.conversation_message_service.create_message(system_message))
            if system_message
            else None,
        )

        # Handle unified queue messaging for files and/or text content
        if files or (content and intention == ConversationMessageIntention.EXTRACTION):
            document_data = DocumentCreationRequest(
                conversation_id=user_message.conversation_id,
                files=files or [],
                message_id=response.user.id,
            )

            await self.extracted_data_service.ldmf_country_service.list(
                token
            )  # Initialize LDMF countries cache and upload to blob storage

            # Use unified approach: send text content for extraction along with files
            document_responses = await self.document_service.create_combined_message(document_data, content)

            if document_responses:
                response.files = document_responses

        if settings.append_collected_data_to_message_response:
            response.collected_data = conversation_data.aggregated_data.model_dump()

        return response

    async def _ensure_previous_message_processed(self, conversation_id: UUID) -> None:
        """
        Ensure the previous message has been fully processed before proceeding.

        Args:
            conversation_id: The conversation ID

        Raises:
            ConversationDataInconsistencyError: If the previous message is still being processed
        """
        try:
            last_message = await self.conversation_message_repository.get_last(conversation_id)
        except EntityNotFoundError:
            last_message = None

        if (
            last_message
            and last_message.role == MessageRole.USER
            and last_message.type
            in (
                MessageType.FILE,
                MessageType.TEXT_WITH_FILE,
            )
        ):
            statuses = await self.processing_message_repository.get_message_processing_statuses(last_message.id)
            if ProcessingStatus.DocumentIsCorrupted in statuses:
                message_data = MessageValidator(
                    conversation_id=last_message.conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content='',
                    selected_option=None,
                    files=None,
                    system_reply_type=None,
                )

                await self.conversation_message_repository.create(message_data)

            elif ProcessingStatus.DocumentExtractionCompleted not in statuses:
                raise ConversationDataInconsistencyError(
                    'Cannot create a new message until the previous one has been processed'
                )
