from abc import ABC, abstractmethod
from typing import Generic, TypeVar
from uuid import UUID

from schemas import MessageValidator, Option


__all__ = ['BaseOptionHandler']


T = TypeVar('T', bound=Option)


class BaseOptionHandler(ABC, Generic[T]):
    """
    Abstract base class for option handlers.

    Defines the interface that all option handlers must implement,
    following SOLID principles and the established layered architecture.
    """

    @abstractmethod
    async def handle(
        self,
        selected_option: T,
        conversation_id: UUID,
        token: str | None = None,
    ) -> MessageValidator:
        """
        Handle the selected option and return a system message response.

        Args:
            selected_option: The selected option from the user
            conversation_id: The conversation ID
            token: Optional user token for external API calls

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            Exception: If there's an error processing the option
        """
        ...
