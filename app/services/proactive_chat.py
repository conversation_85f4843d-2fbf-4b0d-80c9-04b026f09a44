from dataclasses import dataclass
from functools import cached_property
import logging

from constants.extracted_data import FieldCompletionStatus, ProgressStatus, RequiredField
from constants.message import SystemReplyType
from schemas import AggregatedData, ConversationData
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message import SystemMessageSerializer
from schemas.proactive_chat import FieldStatusInfo, ProgressInfo


__all__ = ['ProactiveChatService']

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class ProactiveChatService:
    """Service for proactive chat functionality that guides users through required field collection."""

    conversation_data: ConversationData

    _FIELD_COLLECTION_ORDER = [
        RequiredField.CLIENT_NAME,
        RequiredField.LDMF_COUNTRY,
        RequiredField.ENGAGEMENT_DATES,
        RequiredField.OBJECTIVE_SCOPE,
        RequiredField.OUTCOMES,
    ]
    _REQUIRED_FIELDS_GUIDANCE_MAP = {
        RequiredField.CLIENT_NAME: 'Please provide the client name for this engagement.',
        RequiredField.LDMF_COUNTRY: 'Please specify the Lead Deloitte Member Firm country.',
        RequiredField.ENGAGEMENT_DATES: 'Please provide the engagement start and end dates.',
        RequiredField.OBJECTIVE_SCOPE: 'Please describe the objective and scope of this engagement.',
        RequiredField.OUTCOMES: 'Please describe the outcomes and results of this engagement.',
    }

    _TEXT_FOR_PROGRESS = '\nProgress: {progress.completed}/{progress.total} fields completed ({progress.percentage}%)'
    _TEXT_FOR_MISSING_NEXT_FIELD = 'We need to collect the {next_field_info.display_name}. '

    _TEXT_FOR_PENDING_DATES = 'Got it! I’m not sure of the exact dates you shared. Can you confirm that these start and end dates are correct:'
    _TEXT_FOR_PENDING_CONFIRMATION_NEXT_FIELD = 'Please confirm the {next_field_info.display_name}: {value}. '
    _TEXT_FOR_REMAINING_FIELDS = '\nRemaining fields: {remaining_fields}. '

    system_reply_type: SystemReplyType | None = None
    latest_system_reply: SystemMessageSerializer | None = None
    is_dash_task: bool = False

    add_progress_information: bool = False
    add_next_field_guidance: bool = True
    add_remaining_fields_summary: bool = False

    @property
    def aggregated_data(self) -> AggregatedData:
        return self.conversation_data.aggregated_data

    @property
    def confirmed_data(self) -> ConfirmedData:
        return self.conversation_data.confirmed_data

    def get_proactive_system_message(self) -> tuple[str, SystemReplyType | None]:
        """
        Generate a proactive message that guides the user.
        """

        next_field = self.next_required_field
        progress = self._completion_progress

        # Check if all fields are complete
        if progress.status == ProgressStatus.COMPLETED:
            return '', None

        enhanced_message_content = ''
        proactive_reply_type = None

        if self.add_progress_information:
            # Add progress information
            enhanced_message_content += self._TEXT_FOR_PROGRESS.format(progress=progress)

        if next_field and self.add_next_field_guidance:
            # Add next steps guidance
            next_field_info = self._field_status[next_field]

            if (
                self.is_dash_task
                and next_field == RequiredField.CLIENT_NAME
                and self.aggregated_data.is_client_name_complete
            ):
                enhanced_message_content = ''
                return enhanced_message_content, None

            if next_field_info.status == FieldCompletionStatus.MISSING:
                if next_field == RequiredField.ENGAGEMENT_DATES:
                    proactive_reply_type = SystemReplyType.EXTRACTED_DATA_DATE_INTERVAL_MISSING
                    next_steps = proactive_reply_type.message_text
                elif next_field == RequiredField.LDMF_COUNTRY:
                    proactive_reply_type = SystemReplyType.EXTRACTED_LDMF_NOT_VALID
                    next_steps = proactive_reply_type.message_text
                elif next_field == RequiredField.OBJECTIVE_SCOPE:
                    proactive_reply_type = SystemReplyType.EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING
                    next_steps = proactive_reply_type.message_text
                else:
                    if self.system_reply_type in [
                        SystemReplyType.BRIEF_DESCRIPTION,
                        SystemReplyType.UNCERTAINTY_EMPTY_AGGREGATION,
                    ]:
                        next_steps = ''
                    else:
                        next_steps = self._TEXT_FOR_MISSING_NEXT_FIELD.format(
                            next_field_info=next_field_info,
                        )
                        next_steps += self._REQUIRED_FIELDS_GUIDANCE_MAP[next_field]

                enhanced_message_content += next_steps

            elif next_field_info.status == FieldCompletionStatus.PENDING_CONFIRMATION:
                if next_field == RequiredField.ENGAGEMENT_DATES:
                    next_steps = self._TEXT_FOR_PENDING_DATES
                elif next_field == RequiredField.LDMF_COUNTRY:
                    proactive_reply_type = SystemReplyType.EXTRACTED_LDMF_NOT_VALID
                    next_steps = proactive_reply_type.message_text
                else:
                    REPLY_TYPES_THAT_ASK_FOR_CONFIRMATION = [
                        SystemReplyType.PROVIDE_CLIENT_NAME_UNSURE,
                        SystemReplyType.CLIENT_NOT_FOUND,
                        SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION,
                        SystemReplyType.CLIENT_NAME_SINGLE_FOUND_CONFIRMATION,
                        SystemReplyType.BRIEF_DESCRIPTION,
                        SystemReplyType.DASH_TASK_SELECTED_TEMPLATE,
                    ]
                    if self.system_reply_type in REPLY_TYPES_THAT_ASK_FOR_CONFIRMATION:
                        # PROVIDE_CLIENT_NAME_UNSURE is very similar to _TEXT_FOR_PENDING_CONFIRMATION_NEXT_FIELD,
                        # so no need to duplicate same information.
                        next_steps = ''
                    else:
                        next_steps = self._TEXT_FOR_PENDING_CONFIRMATION_NEXT_FIELD.format(
                            next_field_info=next_field_info,
                            value=next_field_info.value or '',
                        )

                enhanced_message_content += next_steps
            elif next_field_info.status == FieldCompletionStatus.COMPLETED:
                pass
            else:
                raise NotImplementedError(f'Field status: {next_field_info.status} is not supported.')

        if self.add_remaining_fields_summary:
            # Add remaining fields summary
            missing_fields = [
                field for field, info in self._field_status.items() if info.status == FieldCompletionStatus.MISSING
            ]
            if len(missing_fields) > 1:
                enhanced_message_content += self._TEXT_FOR_REMAINING_FIELDS.format(
                    remaining_fields=[self._field_status[field].display_name for field in missing_fields],
                )

        return enhanced_message_content, proactive_reply_type

    @property
    def confirmed_fields_names(self) -> list[str]:
        return [
            field_status_info.display_name
            for field_status_info in self._field_status.values()
            if field_status_info.status == FieldCompletionStatus.COMPLETED
        ]

    # required fields checkers

    @cached_property
    def _field_status(self) -> dict[RequiredField, FieldStatusInfo]:
        """Get the status of all required fields."""

        confirmed_client_name = self.confirmed_data.client_name
        aggregated_client_name = self.aggregated_data.client_name
        proposed_client_name_is_valid = self.confirmed_data.proposed_client_name_is_valid

        if confirmed_client_name:
            client_name_status = FieldCompletionStatus.COMPLETED
            client_name = confirmed_client_name
        elif aggregated_client_name:
            client_name_status = FieldCompletionStatus.PENDING_CONFIRMATION
            client_name = aggregated_client_name
        elif proposed_client_name_is_valid:
            client_name_status = FieldCompletionStatus.PENDING_CONFIRMATION
            client_name = self.confirmed_data.proposed_client_name
        else:
            client_name_status = FieldCompletionStatus.MISSING
            client_name = None

        return {
            RequiredField.CLIENT_NAME: FieldStatusInfo(
                status=client_name_status,
                value=client_name,
                display_name=RequiredField.CLIENT_NAME.verbose_name,
            ),
            RequiredField.LDMF_COUNTRY: self._check_field_status(
                confirmed_value=self.confirmed_data.ldmf_country,
                aggregated_value=self.aggregated_data.ldmf_country,
                display_name=RequiredField.LDMF_COUNTRY.verbose_name,
            ),
            RequiredField.ENGAGEMENT_DATES: self._check_field_status(
                confirmed_value=self._format_date_intervals(self.confirmed_data.date_intervals),
                aggregated_value=self._format_list_of_date_intervals(self.aggregated_data.date_intervals),
                display_name=RequiredField.ENGAGEMENT_DATES.verbose_name,
            ),
            RequiredField.OBJECTIVE_SCOPE: self._check_field_status(
                confirmed_value=self.confirmed_data.objective_and_scope,
                aggregated_value=self.aggregated_data.objective_and_scope,
                display_name=RequiredField.OBJECTIVE_SCOPE.verbose_name,
            ),
            RequiredField.OUTCOMES: self._check_field_status(
                confirmed_value=self.confirmed_data.outcomes,
                aggregated_value=self.aggregated_data.outcomes,
                display_name=RequiredField.OUTCOMES.verbose_name,
            ),
        }

    def _format_date_intervals(self, date_intervals: tuple[str | None, str | None] | None) -> str | None:
        """Format date intervals as a readable string."""
        if not date_intervals or len(date_intervals) != 2:
            return None

        start_date: str | None = date_intervals[0]
        end_date: str | None = date_intervals[1]

        if not start_date and not end_date:
            return None

        return f'{start_date} to {end_date}'

    def _format_list_of_date_intervals(self, date_intervals: list[tuple[str | None, str | None]] | None) -> str | None:
        """Format list of date intervals as a readable string."""
        if not date_intervals:
            return None

        formatted_date_intervals = []
        for date_interval in date_intervals:
            formatted_date_interval = self._format_date_intervals(date_interval)
            if not formatted_date_interval:
                continue
            formatted_date_intervals.append(formatted_date_interval)

        if not formatted_date_intervals:
            return None

        return ', '.join(formatted_date_intervals)

    def _check_field_status(
        self, confirmed_value: str | list | None, aggregated_value: str | list | None, display_name: str
    ) -> FieldStatusInfo:
        """Helper method to check field status based on confirmed and aggregated values."""
        if confirmed_value:
            return FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value=confirmed_value, display_name=display_name
            )
        elif aggregated_value:
            return FieldStatusInfo(
                status=FieldCompletionStatus.PENDING_CONFIRMATION, value=aggregated_value, display_name=display_name
            )
        else:
            return FieldStatusInfo(status=FieldCompletionStatus.MISSING, value=None, display_name=display_name)

    # progress status checkers

    @cached_property
    def next_required_field(self) -> RequiredField | None:
        """Get the next required field that needs attention."""
        for field in self._FIELD_COLLECTION_ORDER:
            field_info = self._field_status[field]
            if field_info.status not in [
                FieldCompletionStatus.COMPLETED,
            ]:
                return field
        return None

    @cached_property
    def _completion_progress(self) -> ProgressInfo:
        """Get completion progress information."""
        total_fields = len(self._FIELD_COLLECTION_ORDER)
        completed_fields = sum(
            1 for info in self._field_status.values() if info.status == FieldCompletionStatus.COMPLETED
        )
        pending_fields = sum(
            1 for info in self._field_status.values() if info.status == FieldCompletionStatus.PENDING_CONFIRMATION
        )
        missing_fields = sum(1 for info in self._field_status.values() if info.status == FieldCompletionStatus.MISSING)

        percentage = int((completed_fields / total_fields) * 100) if total_fields > 0 else 0

        # Determine overall status
        if completed_fields == total_fields:
            status = ProgressStatus.COMPLETED
        elif missing_fields == total_fields:
            status = ProgressStatus.INITIAL
        else:
            status = ProgressStatus.IN_PROGRESS

        return ProgressInfo(
            total=total_fields,
            completed=completed_fields,
            pending_confirmation=pending_fields,
            missing=missing_fields,
            percentage=percentage,
            status=status,
        )
