PYTHONPATH=./
ENVIRONMENT='test'

MOCK_QUALS_CLIENT_API='true'
MOCK_QUALS_CLIENT_GET_REFERENCES_LIST='true'

LOG_LEVEL='INFO'
DEBUG='true'

DB_HOST='127.0.0.1'
DB_PORT='1433'
DB_USER='sa'
DB_PASSWORD='P@ssw0rd_2024_SQL_Secure!'
DB_NAME='genai_quals'
DB_DRIVER='ODBC+Driver+17+for+SQL+Server'

# Azure Blob Storage settings
AZURE_STORAGE_CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;'
AZURE_QUEUE_CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;'
DOCUMENT_BLOB_STORAGE_CONTAINER_NAME='documents'
COUNTRIES_BLOB_STORAGE_CONTAINER_NAME='countries'

ALLOWED_HOSTS='https://kxnextgendevui.deloitteresources.com:4500'
AZURE_CONTENT_QUEUE_NAME='content-analysis-queue'

# Azure AD
# AZURE_AD_TENANT_ID='36da45f1-dd2c-4d1f-af13-5abe46b99921'
# AZURE_AD_API_AUDIENCE='https://qa.kx.deloitte'
# AZURE_AD_REQUIRED_SCOPES='access_as_user,user_impersonation'
AZURE_AD_TENANT_ID='36da45f1-dd2c-4d1f-af13-5abe46b99921'
AZURE_AD_API_AUDIENCE='https://dev.kx.deloitte'
AZURE_AD_REQUIRED_SCOPES='access_as_user,user_impersonation'

# HTTP client settings
HTTP_CLIENT_TIMEOUT='30'
HTTP_CLIENT_FOLLOW_REDIRECTS='true'
HTTP_CLIENT_VERIFY_SSL='true'
HTTP_CLIENT_MAX_CONNECTIONS='100'
HTTP_CLIENT_MAX_KEEPALIVE_CONNECTIONS='20'

# KX Dash API settings
KX_DASH_API_BASE_URL='http://localhost:8000/dash'
KX_DASH_API_MAX_RETRIES='3'
KX_DASH_API_BACKOFF_BASE='60'
KX_DASH_API_BACKOFF_FACTOR='0.5'

# Quals Clients API settings
QUALS_API_BASE_URL='http://localhost:8000/api'

# Industries API settings
INDUSTRIES_API_BASE_URL='http://localhost:8000/client'

# Services API settings
SERVICES_API_BASE_URL='http://localhost:8000/project'

# Roles API settings
ROLES_API_BASE_URL='http://localhost:8000/team-details'

# OpenAI settings
AZURE_OPENAI_ENDPOINT='openai.azure.com'
AZURE_OPENAI_KEY='2weRNxbh8NrMGqhsovd6pQKSS8C19rnTolk0jn40dUdIC8k5zcDHJQQJ99BDAC5RqLJXJ3w3AAABACOGW0FH'
AZURE_OPENAI_DEPLOYMENT='2024-08-01-preview'
AZURE_OPENAI_MODEL='gpt-4o'
AZURE_OPENAI_API_VERSION='2024-08-01-preview'

SIGNAL_R_CONNECTION_STRING='Endpoint=https://qualtestsignalr.service.signalr.net;AccessKey=AMnCNa0WUM7nloEtx6vyKn0BEeaGZa1Y9C9jlNGc8O79GysnO4MjJQQJ99BEACPV0roXJ3w3AAAAASRSnfTg;Version=1.0;'

LDMF_COUNTRIES_BASE_URL="http://localhost:8000/member-firms"


SIGNALR_SETTINGS__HUB_NAME='localhub'
SIGNALR_SETTINGS__CONNECTION_STRING='Endpoint=https://qualtestsignalr.service.signalr.net;AccessKey=AMnCNa0WUM7nloEtx6vyKn0BEeaGZa1Y9C9jlNGc8O79GysnO4MjJQQJ99BEACPV0roXJ3w3AAAAASRSnfTg;Version=1.0;'
 
BLOB_STORAGE_SETTINGS__CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1'
BLOB_STORAGE_SETTINGS__CONTAINER_NAME='documents'
 
QUEUE_SETTINGS__CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;'
QUEUE_SETTINGS__DOCUMENT_PROCESSING_QUEUE='document-processing'
QUEUE_SETTINGS__DOCUMENT_PROCESSING_QUEUE_CHUNKED='document-processing-chunked'
QUEUE_SETTINGS__PROMPT_PROCESSING_QUEUE='prompt-processing'
 
DOCUMENT_INTELLIGENCE_SETTINGS__ENDPOINT='https://devtestgermany.cognitiveservices.azure.com/'
DOCUMENT_INTELLIGENCE_SETTINGS__KEY='609rgQmV2cB93fBJUfXE44eVKR2sMJLmoq2rvQBzb5VY4avg04WJJQQJ99BEACPV0roXJ3w3AAALACOGR7uB'

SUPPORT_URL='https://deloitteglobal.service-now.com'