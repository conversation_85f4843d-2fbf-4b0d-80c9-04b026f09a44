import logging
from uuid import uuid4

from cachetools import TTL<PERSON><PERSON>

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join
from schemas.quals_clients import (
    ClientAPIParamsItem,
    ClientComprehensive,
    ClientCreateRequest,
    ClientCreateResponse,
    ClientSearchItem,
    ClientSearchRequest,
    ClientSearchResponse,
)


__all__ = ['QualsClientsRepository']


logger = logging.getLogger(__name__)


class QualsClientsRepository:
    """Repository for Quals Clients API operations."""

    cache = TTLCache(maxsize=2, ttl=3600)

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Quals Clients Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._base_url = str(settings.quals_clients_api.base_url)
        self._http_client = http_client

    def _generate_mock_search_results(self, search_request: ClientSearchRequest) -> list[ClientSearchItem]:
        """
        Generate mock search results for testing.

        Args:
            search_request: The search request parameters

        Returns:
            list[ClientSearchItem]: Mock search results
        """
        # Generate mock clients based on search query
        mock_clients = [
            ClientSearchItem(
                id=1,
                name=f'Global Corp {search_request.contains}',
                qualsCount=15,
                clientConfidentiality=1,
            ),
            ClientSearchItem(
                id=2,
                name=f'International {search_request.contains} Ltd',
                qualsCount=8,
                clientConfidentiality=1,
            ),
            ClientSearchItem(
                id=3,
                name=f'{search_request.contains} Industries Inc',
                qualsCount=23,
                clientConfidentiality=1,
            ),
        ]

        # Apply pagination
        start_idx = search_request.page_idx * search_request.page_size
        end_idx = start_idx + search_request.page_size
        # Filter for unique client names
        mock_clients = self._unique_list(mock_clients)

        # Sort mock_clients by id in ascending order
        mock_clients.sort(key=lambda client: client.id)
        return mock_clients[start_idx:end_idx]

    def _generate_mock_client_comprehensive(self, create_request: ClientCreateRequest) -> ClientComprehensive:
        """
        Generate a mock comprehensive client object for testing.

        Args:
            create_request: The client creation request

        Returns:
            ClientComprehensive: Mock comprehensive client data
        """
        client_id = int(uuid4().int >> 96)  # Use upper 32 bits of UUID as a unique int

        return ClientComprehensive(
            id=client_id,
            name=create_request.name,
            description=f'Mock description for {create_request.name}',
            primaryLocalIndustry='Technology',
            primaryGlobalIndustry='Information Technology',
            secondaryLocalIndustries=['Software Development', 'Consulting'],
            secondaryGlobalIndustries=['Digital Transformation', 'Cloud Services'],
        )

    async def search_clients(self, search_request: ClientSearchRequest, token: str) -> ClientSearchResponse:
        """
        Search for clients using the Quals Clients API.

        Args:
            search_request: Search parameters including query text and pagination
            token: Bearer token for authentication

        Returns:
            ClientSearchResponse: Search results with pagination info
        """
        if settings.quals_clients_api.mock_client_api_enabled:
            # Mock implementation
            logger.info('Using mock data for client search: %s', search_request.contains)
            mock_results = self._generate_mock_search_results(search_request)

            return ClientSearchResponse(
                clients=mock_results,
                total_count=len(mock_results) + 10,  # Simulate more results available
                page_size=search_request.page_size,
                page_idx=search_request.page_idx,
                exact_match=False,
            )

        # Real implementation (when API is working)
        try:
            url = url_join(self._base_url, 'client/clients')
            params = {
                'contains': search_request.contains,
                'pageSize': search_request.page_size,
                'pageIdx': search_request.page_idx,
            }
            headers = {'Authorization': f'Bearer {token}'}

            logger.info('Searching clients with params: %s', params)
            response_data = (await self._http_client.get(url, params=params, headers=headers)).json()

            # Parse response - API returns array directly, not wrapped in object
            if isinstance(response_data, list):
                clients = [ClientSearchItem(**client) for client in response_data]
                total_count = len(clients)  # API doesn't provide total count
            else:
                clients = [ClientSearchItem(**client) for client in response_data.get('clients', [])]
                total_count = response_data.get('totalCount', len(clients))

            clients.sort(key=lambda client: client.id)

            # Filter for unique client names
            clients = self._unique_list(clients)

            # Sort clients by id in ascending order
            exact_match = any(client.name.lower() == search_request.contains.lower() for client in clients)

            return ClientSearchResponse(
                clients=clients,
                total_count=total_count,
                page_size=search_request.page_size,
                page_idx=search_request.page_idx,
                exact_match=exact_match,
            )

        except Exception:
            logger.exception('Error searching clients')
            raise

    def _unique_list(self, clients):
        unique_clients = {}
        for client in clients:
            unique_clients.setdefault(client.name, client)
        return list(unique_clients.values())

    async def create_client(self, create_request: ClientCreateRequest, token: str) -> ClientCreateResponse:
        """
        Create a new client using the Quals Clients API.

        Args:
            create_request: Client data for creation
            token: Bearer token for authentication

        Returns:
            ClientCreateResponse: Created client data
        """
        if settings.quals_clients_api.mock_client_api_enabled:
            # Mock implementation
            logger.info('Using mock data for client creation: %s', create_request.name)
            mock_client = self._generate_mock_client_comprehensive(create_request)

            return ClientCreateResponse(client=mock_client, success=True, message='Client created successfully (mock)')

        # Real implementation (when API is working)
        url = url_join(self._base_url, 'clients')

        try:
            # Try query parameter approach first (as mentioned in requirements)
            params = {'name': create_request.name}
            headers = {'Authorization': f'Bearer {token}'}

            logger.info('Creating client: %s', create_request.name)
            response_data = (await self._http_client.post(url, params=params, headers=headers)).json()
            logger.info('Client created: %s', response_data)

            # Parse response and return structured data
            client = ClientComprehensive(**response_data)

            return ClientCreateResponse(client=client, success=True, message='Client created successfully')

        except Exception:
            logger.exception("Error creating client '%s'", create_request.name)
            raise

    async def get_references_list(self, token: str) -> list[ClientAPIParamsItem]:
        """
        Get a list of references from the Quals Clients API.

        Args:
            token: Bearer token for authentication

        Returns:
            List of ClientAPIParamsItem objects
        """
        if settings.quals_clients_api.mock_client_get_references_list_enabled:
            # Mock implementation
            logger.info('Using mock data for references list')
            return [
                ClientAPIParamsItem(id=868, name='Yes', catalogue_number=7, catalogue_type=7, sort_order=1),
                ClientAPIParamsItem(id=985, name='No', catalogue_number=7, catalogue_type=7, sort_order=2),
                ClientAPIParamsItem(id=869, name='In some cases', catalogue_number=7, catalogue_type=7, sort_order=3),
                ClientAPIParamsItem(id=867, name="I don't know", catalogue_number=7, catalogue_type=7, sort_order=4),
            ]

        # Real implementation (when API is working)
        references_items = self.cache.get('references_items')
        if references_items is not None:
            return references_items

        url = url_join(self._base_url, 'client-references')
        headers = {'Authorization': f'Bearer {token}'}

        try:
            response_data = (await self._http_client.get(url, headers=headers)).json()
            reference_items = [ClientAPIParamsItem(**ref) for ref in response_data]

        except Exception:
            logger.exception('Error getting references list')
            raise

        self.cache['references_items'] = reference_items
        return reference_items

    async def get_client_sharing_list(self, token: str) -> list[ClientAPIParamsItem]:
        """
        Get a list of client sharing options from the Quals Clients API.

        Args:
            token: Bearer token for authentication

        Returns:
            List of ClientAPIParamsItem objects
        """
        if settings.quals_clients_api.mock_client_get_references_list_enabled:
            # Mock implementation
            logger.info('Using mock data for client_sharing list')
            return [
                ClientAPIParamsItem(
                    catalogue_number=69,
                    catalogue_type=69,
                    sort_order=0,
                    name='Client name may be shared internally',
                    id=879,
                ),
                ClientAPIParamsItem(
                    catalogue_number=69,
                    catalogue_type=69,
                    sort_order=0,
                    name='Client name may NOT be shared internally',
                    id=880,
                ),
            ]

        # Real implementation (when API is working)
        name_sharing = self.cache.get('name_sharing')
        if name_sharing is not None:
            return name_sharing

        url = url_join(self._base_url, 'internal-using')
        headers = {'Authorization': f'Bearer {token}'}

        try:
            response_data = (await self._http_client.get(url, headers=headers)).json()
            client_sharing_items = [ClientAPIParamsItem(**ref) for ref in response_data]

        except Exception:
            logger.exception('Error getting client_sharing list')
            raise

        self.cache['name_sharing'] = client_sharing_items
        return client_sharing_items
